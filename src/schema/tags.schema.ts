import { z } from "zod/v4";

export const TagsSchema = z.object({
  title: z
    .string()
    .trim()
    .min(1, "Tag is required.")
    .min(3, "Tag must be of at least 3 characters.")
    .regex(
      /^[a-z0-9\s-]+$/,
      "Tags can only contain lowercase letters, numbers, spaces, and hyphens.",
    )
    .max(15, "Tags must not exceed 15 characters."),
});

export type TagsTypes = z.infer<typeof TagsSchema>;
