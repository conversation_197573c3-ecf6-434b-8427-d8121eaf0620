import { z } from "zod";

export const SummaryRequestSchema = z.object({
  contentId: z
    .string()
    .min(1, { message: "Content ID is required." })
    .regex(/^[0-9a-fA-F]{24}$/, {
      message:
        "Invalid Content ID format. Must be a 24-character hexadecimal string.",
    }),

  type: z
    .enum(["quick", "detailed"], {
      errorMap: () => ({ message: "Summary type must be either 'quick' or 'detailed'." }),
    }),

  forceRegenerate: z
    .boolean()
    .optional()
    .default(false),
});

export const SummaryResponseSchema = z.object({
  id: z.string(),
  contentId: z.string(),
  userId: z.string(),
  type: z.enum(["quick", "detailed"]),
  summary: z.string(),
  originalUrl: z.string(),
  contentTitle: z.string(),
  tokensUsed: z.number(),
  processingTime: z.number(),
  model: z.string(),
  status: z.enum(["pending", "completed", "failed"]),
  errorMessage: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type SummaryRequestInput = z.infer<typeof SummaryRequestSchema>;
export type SummaryResponse = z.infer<typeof SummaryResponseSchema>;
