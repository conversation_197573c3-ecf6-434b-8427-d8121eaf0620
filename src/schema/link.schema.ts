import { z } from "zod";

export const LinkSchema = z.object({
  contentId: z
    .string()
    .min(1, { message: "Content ID is required." })
    .regex(/^[0-9a-fA-F]{24}$/, {
      message:
        "Invalid Content ID format. Must be a 24-character hexadecimal string.",
    }),

  userId: z
    .string()
    .min(1, { message: "User ID is required." })
    .regex(/^[0-9a-fA-F]{24}$/, {
      message:
        "Invalid User ID format. Must be a 24-character hexadecimal string.",
    }),

  expiresAt: z.coerce
    .date()
    .optional()
    .refine(
      (val) => {
        if (val === undefined) return true;
        return val.getTime() > Date.now();
      },
      { message: "Expiration date must be in the future." },
    ),

  maxAccesses: z
    .number()
    .int("Maximum accesses must be an integer.")
    .min(1, { message: "Maximum accesses must be at least 1." })
    .optional(),

  isActive: z.boolean().optional().default(false),
});

export type LinkInput = z.infer<typeof LinkSchema>;
