import { Zod<PERSON>rror } from "zod/v4";
import type { AuthenticatedRequest } from "../middleware/auth.middleware";
import { LinkSchema, type LinkInput } from "../schema/link.schema";
import {
  CreateShareableLinkService,
  DeleteShareableLinkService,
  GetShareableLinkByCodeService,
} from "../services/link.service";
import { AppError } from "../utils/error";
import type { BunRequest } from "bun";

/**
 * Create a shareable link for content
 * POST /api/v1/links
 */
export async function CreateShareableLinkController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const userId = req.user!.id;

    const validLinkData: LinkInput = LinkSchema.parse(body);

    const shareableLink = await CreateShareableLinkService(
      userId,
      validLinkData
    );

    return Response.json(
      {
        success: true,
        message: "Shareable link created successfully.",
        data: {
          ...shareableLink,
          shareUrl: `${
            process.env.APP_BASE_URL || "http://localhost:3000"
          }/share/${shareableLink!.shortCode}`,
        },
      },
      {
        status: 201,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Create shareable link error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Access shared content via short code (public endpoint)
 * GET /share/:shortCode
 */
export async function AccessSharedContentController(
  req: BunRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const shortCode = pathParts[pathParts.length - 1];

    if (!shortCode) {
      return Response.json(
        {
          success: false,
          message: "Short code is required.",
        },
        { status: 400 }
      );
    }

    const sharedContent = await GetShareableLinkByCodeService(shortCode);

    return Response.json(
      {
        success: true,
        message: "Shared content accessed successfully.",
        data: sharedContent,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Access shared content error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Delete shareable link
 * DELETE /api/v1/links/:id
 */
export async function DeleteShareableLinkController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const linkId = pathParts[pathParts.length - 1];

    if (!linkId) {
      return Response.json(
        {
          success: false,
          message: "Link ID is required.",
        },
        { status: 400 }
      );
    }

    const userId = req.user!.id;
    const deletedLink = await DeleteShareableLinkService(linkId, userId);

    return Response.json(
      {
        success: true,
        message: "Shareable link deleted successfully.",
        data: deletedLink,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Delete shareable link error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
