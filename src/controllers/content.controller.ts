import {
  ContentIdSchema,
  ContentSchema,
  type ContentIdType,
  type ContentTypes,
} from "../schema/content.schema";
import { ZodError } from "zod/v4";
import type { AuthenticatedRequest } from "../middleware/auth.middleware";
import {
  BulkDeleteContentService,
  CreateContentService,
  DeleteContentService,
  GetContentByIdService,
  GetContentService,
  GetContentStatsService,
  GetUserContentService,
  UpdateUserContentService,
} from "../services/content.service";
import { AppError, BadRequestError } from "../utils/error";

/**
 * Create single content  (with user authorization)
 * POST /api/v1/user/content
 */
export async function ContentCreateController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const id = req.user?.id;

    const validContentData: ContentTypes = ContentSchema.parse(body);

    const contentData = await CreateContentService(id, {
      ...validContentData,
      userId: id,
    });

    return Response.json(
      { success: true, message: "Link saved successfull.", contentData },
      {
        status: 200,
        statusText: "Link saved.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

/**
 * Fetch User content with pagination (with user authorization)
 * GET /api/v1/user/content?page=2&limit=10&search=test&type=abc&sortBy=updatedAt&sortOrder=desc
 */
export async function GetUserContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const id = req.user?.id;
    const url = new URL(req.url);

    // Parse query parameters
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const search = url.searchParams.get("search") || undefined;
    const type = url.searchParams.get("type") || undefined;
    const sortBy = url.searchParams.get("sortBy") || "createdAt";
    const sortOrder = (url.searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      throw new BadRequestError(
        "Invalid pagination parameters. Page must be >= 1, limit must be 1-100.",
        400
      );
    }

    const result = await GetUserContentService(
      id,
      page,
      limit,
      search,
      type,
      sortBy,
      sortOrder
    );

    return Response.json(
      {
        success: true,
        message: "Content fetch successfull.",
        contentData: result.content,
        pagination: result.pagination,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Get user content error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * update single content by id (with user authorization)
 * put /api/v1/user/content:id
 */
export async function UpdateUserContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const urlParts = new URL(req.url).pathname.split("/");
    const contId = urlParts[urlParts.length - 1];

    const contentId: ContentIdType = ContentIdSchema.parse(contId);
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("Unauthorized: User ID not found in request.", 401);
    }

    const body = await req.json();

    const validContentData: ContentTypes = ContentSchema.parse(body);

    const updatedData = await UpdateUserContentService(
      contentId,
      userId,
      validContentData
    );

    return Response.json(
      {
        success: true,
        message: "Content update successfull.",
        contentData: updatedData,
      },
      {
        status: 200,
        statusText: "Content update successfull.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

/**
 * Fetch single content by id 
 
 * route not assigned yet just made this function
 */
export async function GetContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const urlParts = new URL(req.url).pathname.split("/");
    const contId = urlParts[urlParts.length - 1];

    const contentId: ContentIdType = ContentIdSchema.parse(contId);

    const contentData = await GetContentService(contentId);

    return Response.json(
      {
        success: true,
        message: "Content fetch successfull.",
        contentData,
      },
      {
        status: 200,
        statusText: "Content fetch successfull.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

/**
 * Get single content by ID (with user authorization)
 * GET /api/v1/user/content/:id
 */
export async function GetContentByIdController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const contentId = pathParts[pathParts.length - 1];

    if (!contentId) {
      throw new BadRequestError("Content ID is required.", 400);
    }

    const userId = req.user!.id;
    const content = await GetContentByIdService(contentId, userId);

    return Response.json(
      {
        success: true,
        message: "Content retrieved successfully.",
        data: content,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Get content by ID error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Delete content by ID
 * DELETE /api/v1/user/content/:id
 */
export async function DeleteContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const contentId = pathParts[pathParts.length - 1];

    if (!contentId) {
      throw new BadRequestError("Content ID is required.", 400);
    }

    const userId = req.user!.id;
    const deletedContent = await DeleteContentService(contentId, userId);

    return Response.json(
      {
        success: true,
        message: "Content deleted successfully.",
        contentData: deletedContent,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Delete content error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Bulk delete content
 * DELETE /api/v1/user/content/bulk
 */
export async function BulkDeleteContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const { contentIds } = body as { contentIds: string[] };

    if (!Array.isArray(contentIds) || contentIds.length === 0) {
      throw new BadRequestError(
        "Content IDs array is required and cannot be empty.",
        400
      );
    }

    const userId = req.user!.id;
    const result = await BulkDeleteContentService(contentIds, userId);

    return Response.json(
      {
        success: true,
        message: `Successfully deleted ${result.deletedCount} out of ${result.requestedCount} content items.`,
        contentData: result,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Bulk delete content error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Get content statistics
 * GET /api/v1/user/content/stats
 */
export async function GetContentStatsController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const userId = req.user!.id;
    const stats = await GetContentStatsService(userId);

    return Response.json(
      {
        success: true,
        message: "Content statistics retrieved successfully.",
        statsData: stats,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Get content stats error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
