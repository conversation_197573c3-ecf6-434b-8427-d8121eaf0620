import type { BunRequest } from "bun";
import { AppError } from "../utils/error";

// Export all middleware
export * from "./auth.middleware";
export * from "./cors.middleware";
export * from "./logging.middleware";
export * from "./rateLimit.middleware";

// Middleware composition types
export type MiddlewareFunction = (
  req: BunRequest,
  next: () => Promise<Response>
) => Promise<Response>;

export type RouteHandler = (req: BunRequest) => Promise<Response>;

/**
 * Compose multiple middleware functions into a single handler
 */
export function composeMiddleware(
  ...middlewares: MiddlewareFunction[]
): (handler: <PERSON><PERSON>and<PERSON>) => RouteHandler {
  return function (handler: RouteHandler): RouteHandler {
    return async function (req: BunRequest): Promise<Response> {
      let index = 0;

      async function next(): Promise<Response> {
        if (index >= middlewares.length) {
          return await handler(req);
        }

        const middleware = middlewares[index++];
        return await middleware!(req, next);
      }

      return await next();
    };
  };
}

/**
 * Apply middleware to a route handler
 */
export function withMiddleware(
  handler: Route<PERSON><PERSON>ler,
  ...middlewares: MiddlewareFunction[]
): RouteHandler {
  return composeMiddleware(...middlewares)(handler);
}

/**
 * Create a middleware pipeline
 */
export class MiddlewarePipeline {
  private middlewares: MiddlewareFunction[] = [];

  use(middleware: MiddlewareFunction): this {
    this.middlewares.push(middleware);
    return this;
  }

  execute(handler: RouteHandler): RouteHandler {
    return composeMiddleware(...this.middlewares)(handler);
  }

  clone(): MiddlewarePipeline {
    const pipeline = new MiddlewarePipeline();
    pipeline.middlewares = [...this.middlewares];
    return pipeline;
  }
}

/**
 * Common middleware combinations
 */

// Basic middleware stack for all routes
export function createBasicMiddleware() {
  return composeMiddleware(errorBoundaryMiddleware, securityHeadersMiddleware);
}

// API middleware stack
export function createApiMiddleware() {
  const { corsMiddleware } = require("./cors.middleware");
  const { simpleLoggingMiddleware } = require("./logging.middleware");
  const { apiRateLimitMiddleware } = require("./rateLimit.middleware");

  return composeMiddleware(
    errorBoundaryMiddleware,
    securityHeadersMiddleware,
    corsMiddleware(),
    simpleLoggingMiddleware,
    apiRateLimitMiddleware
  );
}

// Auth-protected middleware stack
export function createProtectedMiddleware() {
  const { corsMiddleware } = require("./cors.middleware");
  const { simpleLoggingMiddleware } = require("./logging.middleware");
  const { apiRateLimitMiddleware } = require("./rateLimit.middleware");
  const { authMiddleware } = require("./auth.middleware");

  return composeMiddleware(
    errorBoundaryMiddleware,
    securityHeadersMiddleware,
    corsMiddleware(),
    simpleLoggingMiddleware,
    apiRateLimitMiddleware,
    authMiddleware
  );
}

/**
 * Error boundary middleware
 */
export async function errorBoundaryMiddleware(
  req: BunRequest,
  next: () => Promise<Response>
): Promise<Response> {
  return next().catch((error: Error) => {
    console.error("Unhandled error in middleware:", error);

    let statusCode = 500;
    let message = "Internal server error";
    let stack = undefined; // By default, don't expose stack

    if (error instanceof AppError) {
      // If it's an operational error we've explicitly thrown
      statusCode = error.statusCode;
      message = error.message; // Use the specific message from the AppError
      // Only include stack for non-operational errors in development
      if (!error.isOperational && process.env.NODE_ENV !== "production") {
        stack = error.stack;
      }
    } else {
      // For unexpected, non-AppError instances (true programming errors)
      if (process.env.NODE_ENV !== "production") {
        // In development, show the actual message for unexpected errors
        message = error.message;
        stack = error.stack;
      }
      // In production, keep 'Internal server error' for these unexpected errors
    }

    return Response.json(
      {
        success: false,
        message: message,
        ...(stack && { stack: stack }), // Conditionally include stack
      },
      { status: statusCode }
    );
  });
}

/**
 * Request validation middleware
 */
export async function requestValidationMiddleware(
  req: BunRequest,
  next: () => Promise<Response>
): Promise<Response> {
  // Add request validation logic here
  // For example, check content-type for POST requests
  if (req.method === "POST" || req.method === "PUT" || req.method === "PATCH") {
    const contentType = req.headers.get("Content-Type");
    if (!contentType || !contentType.includes("application/json")) {
      return Promise.resolve(
        Response.json(
          {
            success: false,
            message: "Content-Type must be application/json",
          },
          { status: 400 }
        )
      );
    }
  }

  return next();
}

/**
 * Security headers middleware
 */
export async function securityHeadersMiddleware(
  req: BunRequest,
  next: () => Promise<Response>
): Promise<Response> {
  return next().then((response) => {
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers),
    });

    // Add security headers
    newResponse.headers.set("X-Content-Type-Options", "nosniff");
    newResponse.headers.set("X-Frame-Options", "DENY");
    newResponse.headers.set("X-XSS-Protection", "1; mode=block");
    newResponse.headers.set(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains"
    );
    newResponse.headers.set(
      "Referrer-Policy",
      "strict-origin-when-cross-origin"
    );

    return newResponse;
  });
}
