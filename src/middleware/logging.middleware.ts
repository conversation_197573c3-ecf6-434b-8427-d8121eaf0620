import type { BunRequest } from "bun";

export interface LoggingOptions {
  includeBody?: boolean;
  includeHeaders?: boolean;
  excludePaths?: string[];
  logLevel?: "info" | "debug" | "warn" | "error";
}

const defaultLoggingOptions: LoggingOptions = {
  includeBody: false,
  includeHeaders: false,
  excludePaths: ["/api/status", "/favicon.ico"],
  logLevel: "info",
};

/**
 * Logging middleware for request/response logging
 */
export function loggingMiddleware(options: LoggingOptions = {}) {
  const config = { ...defaultLoggingOptions, ...options };

  return async function (
    req: BunRequest,
    next: () => Promise<Response>
  ): Promise<Response> {
    const startTime = Date.now();
    const url = new URL(req.url);
    const path = url.pathname;

    // Skip logging for excluded paths
    if (config.excludePaths?.includes(path)) {
      return await next();
    }

    // Log request
    const requestLog: any = {
      method: req.method,
      path: path,
      query: url.search,
      userAgent: req.headers.get("User-Agent"),
      ip:
        req.headers.get("X-Forwarded-For") ||
        req.headers.get("X-Real-IP") ||
        "unknown",
      timestamp: new Date().toISOString(),
    };

    if (config.includeHeaders) {
      requestLog.headers = Object.fromEntries(req.headers.entries());
    }

    if (config.includeBody && req.method !== "GET") {
      try {
        // Clone the request to read body without consuming it
        const clonedReq = req.clone();
        const body = await clonedReq.text();
        if (body) {
          // Don't log sensitive data
          if (
            path.includes("login") ||
            path.includes("signup") ||
            path.includes("password")
          ) {
            requestLog.body = "[REDACTED - Contains sensitive data]";
          } else {
            requestLog.body = body;
          }
        }
      } catch (error) {
        requestLog.bodyError = "Failed to read request body";
      }
    }

    console.log(`📥 ${req.method} ${path}`, requestLog);

    const response: Response = await next();

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Log response
    const responseLog: any = {
      status: response.status,
      statusText: response.statusText,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
    };

    // Color code based on status
    let logSymbol = "📤";
    let logLevel = "info";

    if (response.status >= 500) {
      logSymbol = "🔴";
      logLevel = "error";
    } else if (response.status >= 400) {
      logSymbol = "🟡";
      logLevel = "warn";
    } else if (response.status >= 300) {
      logSymbol = "🔵";
      logLevel = "info";
    } else {
      logSymbol = "🟢";
      logLevel = "info";
    }

    const logMessage = `${logSymbol} ${req.method} ${path} - ${response.status} (${duration}ms)`;

    switch (logLevel) {
      case "error":
        console.error(logMessage, responseLog);
        break;
      case "warn":
        console.warn(logMessage, responseLog);
        break;
      default:
        console.log(logMessage, responseLog);
    }

    return response;
  };
}

/**
 * Simple request logging middleware
 */
export const simpleLoggingMiddleware = loggingMiddleware({
  includeBody: false,
  includeHeaders: false,
  excludePaths: ["/api/status", "/favicon.ico", "/"],
});

/**
 * Detailed logging middleware for development
 */
export const detailedLoggingMiddleware = loggingMiddleware({
  includeBody: true,
  includeHeaders: true,
  excludePaths: ["/api/status"],
  logLevel: "debug",
});

/**
 * Production logging middleware with minimal output
 */
export const productionLoggingMiddleware = loggingMiddleware({
  includeBody: false,
  includeHeaders: false,
  excludePaths: ["/api/status", "/favicon.ico", "/", "/api/health"],
  logLevel: "warn",
});
