# 🛡️ Middleware System for Bun Server

A comprehensive, production-ready middleware system designed specifically for Bun.js servers with TypeScript support.

## 📁 File Structure

```
middleware/
├── auth.middleware.ts      # JWT authentication middleware
├── cors.middleware.ts      # CORS handling middleware
├── logging.middleware.ts   # Request/response logging
├── rateLimit.middleware.ts # Rate limiting protection
├── index.ts               # Main exports and utilities
├── examples.ts            # Usage examples
└── README.md             # This file
```

## 🚀 Quick Start

### 1. Basic Usage

```typescript
import { withMiddleware, authMiddleware, corsMiddleware } from "./middleware";

// Protected route
const protectedHandler = withMiddleware(
  async (req) => {
    const user = req.user; // User attached by auth middleware
    return Response.json({ message: "Protected content", user });
  },
  corsMiddleware(),
  authMiddleware
);
```

### 2. Integration with Bun Server

```typescript
import { serve } from "bun";
import { withMiddleware, authMiddleware, corsMiddleware, simpleLoggingMiddleware } from "./middleware";

const server = serve({
  port: 3000,
  routes: {
    "/api/protected": {
      GET: withMiddleware(
        async (req) => Response.json({ data: "secret" }),
        corsMiddleware(),
        simpleLoggingMiddleware,
        authMiddleware
      )
    }
  }
});
```

## 🔧 Middleware Components

### 1. Authentication Middleware (`auth.middleware.ts`)

**Purpose**: Validates JWT tokens and attaches user data to requests.

**Features**:
- ✅ Bearer token support (`Authorization: Bearer <token>`)
- ✅ Cookie-based authentication
- ✅ User data attachment to request object
- ✅ Optional authentication mode
- ✅ Automatic token validation

**Usage**:
```typescript
// Required authentication
const handler = withMiddleware(myHandler, authMiddleware);

// Optional authentication
const handler = withMiddleware(myHandler, optionalAuthMiddleware);
```

**Request Enhancement**:
```typescript
interface AuthenticatedRequest extends BunRequest {
  user?: {
    id: string;
    fullName: string;
    username: string;
  };
}
```

### 2. CORS Middleware (`cors.middleware.ts`)

**Purpose**: Handles Cross-Origin Resource Sharing for web applications.

**Features**:
- ✅ Configurable origins (single, multiple, or wildcard)
- ✅ Automatic OPTIONS preflight handling
- ✅ Credentials support
- ✅ Custom headers configuration
- ✅ Environment-specific settings

**Usage**:
```typescript
// Development (allow all origins)
const devCors = corsMiddleware({ origin: true });

// Production (specific domains)
const prodCors = corsMiddleware({ 
  origin: ["https://yourdomain.com"],
  credentials: true 
});
```

### 3. Logging Middleware (`logging.middleware.ts`)

**Purpose**: Comprehensive request/response logging with security considerations.

**Features**:
- ✅ Request/response timing
- ✅ Color-coded status indicators
- ✅ Sensitive data protection
- ✅ Configurable log levels
- ✅ IP address tracking
- ✅ User agent logging

**Usage**:
```typescript
// Simple logging
const handler = withMiddleware(myHandler, simpleLoggingMiddleware);

// Detailed logging (development)
const handler = withMiddleware(myHandler, detailedLoggingMiddleware);

// Production logging (minimal)
const handler = withMiddleware(myHandler, productionLoggingMiddleware);
```

**Log Output Example**:
```
📥 POST /api/login { method: 'POST', path: '/api/login', ip: '127.0.0.1' }
🟢 POST /api/login - 200 (45ms) { status: 200, duration: '45ms' }
```

### 4. Rate Limiting Middleware (`rateLimit.middleware.ts`)

**Purpose**: Protects against abuse with configurable rate limiting.

**Features**:
- ✅ IP-based limiting
- ✅ Configurable time windows
- ✅ Custom key generation
- ✅ Rate limit headers
- ✅ Different limits for different endpoints
- ✅ Memory-based storage (Redis-ready)

**Usage**:
```typescript
// General API rate limiting
const handler = withMiddleware(myHandler, apiRateLimitMiddleware);

// Strict auth rate limiting
const handler = withMiddleware(loginHandler, authRateLimitMiddleware);

// Custom rate limiting
const customLimit = rateLimitMiddleware({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100,
  message: "Too many requests"
});
```

## 🔗 Middleware Composition

### Using `withMiddleware`

```typescript
const handler = withMiddleware(
  async (req) => Response.json({ data: "Hello" }),
  middleware1,
  middleware2,
  middleware3
);
```

### Using `composeMiddleware`

```typescript
const middlewareStack = composeMiddleware(
  corsMiddleware(),
  loggingMiddleware(),
  authMiddleware
);

const handler = middlewareStack(async (req) => {
  return Response.json({ data: "Hello" });
});
```

### Using `MiddlewarePipeline`

```typescript
const pipeline = new MiddlewarePipeline()
  .use(corsMiddleware())
  .use(loggingMiddleware())
  .use(authMiddleware);

const handler = pipeline.execute(async (req) => {
  return Response.json({ data: "Hello" });
});
```

## 🛠️ Utility Middlewares

### Error Boundary
```typescript
// Catches and handles all errors
const handler = withMiddleware(myHandler, errorBoundaryMiddleware);
```

### Security Headers
```typescript
// Adds security headers (XSS protection, etc.)
const handler = withMiddleware(myHandler, securityHeadersMiddleware);
```

### Request Validation
```typescript
// Validates Content-Type for POST/PUT requests
const handler = withMiddleware(myHandler, requestValidationMiddleware);
```

## 🌍 Environment Configuration

### Development
```typescript
const corsConfig = corsMiddleware({ origin: true }); // Allow all
const logging = detailedLoggingMiddleware; // Verbose logging
```

### Production
```typescript
const corsConfig = corsMiddleware({ 
  origin: ["https://yourdomain.com"],
  credentials: true 
});
const logging = productionLoggingMiddleware; // Minimal logging
```

## 🔒 Security Features

1. **JWT Validation**: Secure token verification with proper error handling
2. **Rate Limiting**: Protection against brute force and DDoS attacks
3. **CORS Protection**: Prevents unauthorized cross-origin requests
4. **Security Headers**: XSS protection, content type sniffing prevention
5. **Sensitive Data Protection**: Automatic redaction in logs
6. **Error Handling**: Secure error responses without information leakage

## 📊 Performance Considerations

- **Memory Usage**: Rate limiting uses in-memory storage (consider Redis for production)
- **Async Operations**: All middleware is fully async-compatible
- **Error Handling**: Graceful degradation on middleware failures
- **Logging**: Configurable verbosity to reduce I/O overhead

## 🚨 Common Issues & Solutions

### Issue: CORS Preflight Failures
**Solution**: Ensure OPTIONS method is handled:
```typescript
"/api/endpoint": {
  GET: handler,
  POST: handler,
  OPTIONS: corsMiddleware() // Add this
}
```

### Issue: Rate Limiting in Multi-Instance Setup
**Solution**: Use Redis for shared rate limiting storage:
```typescript
// TODO: Implement Redis-based rate limiting for production
```

### Issue: Authentication Token Not Found
**Solution**: Check both Authorization header and cookies:
```typescript
// The auth middleware automatically checks both sources
```

## 📝 Next Steps

1. **Copy middleware files** to your project
2. **Update your server** using the examples
3. **Configure environment variables**
4. **Test with your existing endpoints**
5. **Monitor logs and adjust rate limits**

## 🤝 Contributing

Feel free to extend the middleware system with additional features like:
- Redis-based rate limiting
- Database session management
- Advanced security headers
- Request/response caching
- API versioning middleware
