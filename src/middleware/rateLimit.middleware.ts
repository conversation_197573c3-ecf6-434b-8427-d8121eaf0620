import type { BunRequest } from "bun";

export interface RateLimitOptions {
  windowMs?: number; // Time window in milliseconds
  maxRequests?: number; // Maximum requests per window
  message?: string; // Error message when limit exceeded
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  keyGenerator?: (req: BunRequest) => string; // Function to generate unique keys
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const defaultRateLimitOptions: Required<RateLimitOptions> = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per window
  message: "Too many requests, please try again later.",
  skipSuccessfulRequests: false,
  skipFailedRequests: false,
  keyGenerator: (req: BunRequest) => {
    // Use IP address as default key
    return (
      req.headers.get("X-Forwarded-For") ||
      req.headers.get("X-Real-IP") ||
      req.headers.get("CF-Connecting-IP") ||
      "unknown"
    );
  },
};

/**
 * In-memory rate limiting middleware
 * Note: This is not suitable for production with multiple server instances
 * Consider using Redis or another shared store for production
 */
export function rateLimitMiddleware(options: RateLimitOptions = {}) {
  const config = { ...defaultRateLimitOptions, ...options };
  const store: RateLimitStore = {};

  // Clean up expired entries every 5 minutes
  setInterval(() => {
    const now = Date.now();
    Object.keys(store).forEach((key) => {
      if (store[key]!.resetTime < now) {
        delete store[key];
      }
    });
  }, 5 * 60 * 1000);

  return async function (
    req: BunRequest,
    next: () => Promise<Response>
  ): Promise<Response> {
    const key = config.keyGenerator(req);
    const now = Date.now();
    const windowStart = now - config.windowMs;

    // Initialize or reset if window has passed
    if (!store[key] || store[key].resetTime < now) {
      store[key] = {
        count: 0,
        resetTime: now + config.windowMs,
      };
    }

    // Check if limit exceeded
    if (store[key].count >= config.maxRequests) {
      const resetTime = new Date(store[key].resetTime);
      return Response.json(
        {
          success: false,
          message: config.message,
          retryAfter: Math.ceil((store[key].resetTime - now) / 1000),
        },
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": config.maxRequests.toString(),
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": resetTime.toISOString(),
            "Retry-After": Math.ceil(
              (store[key].resetTime - now) / 1000
            ).toString(),
          },
        }
      );
    }

    // Increment counter
    store[key].count++;

    // Execute the request
    const response = await next();

    // Optionally skip counting based on response status
    if (
      (config.skipSuccessfulRequests && response.status < 400) ||
      (config.skipFailedRequests && response.status >= 400)
    ) {
      store[key].count--;
    }

    // Add rate limit headers to response
    const remaining = Math.max(0, config.maxRequests - store[key].count);
    const resetTime = new Date(store[key].resetTime);

    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers),
    });

    newResponse.headers.set("X-RateLimit-Limit", config.maxRequests.toString());
    newResponse.headers.set("X-RateLimit-Remaining", remaining.toString());
    newResponse.headers.set("X-RateLimit-Reset", resetTime.toISOString());

    return newResponse;
  };
}

/**
 * Strict rate limiting for authentication endpoints
 */
export const authRateLimitMiddleware = rateLimitMiddleware({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 attempts per 15 minutes
  message: "Too many authentication attempts, please try again later.",
  keyGenerator: (req: BunRequest) => {
    // Combine IP and user agent for auth endpoints
    const ip =
      req.headers.get("X-Forwarded-For") ||
      req.headers.get("X-Real-IP") ||
      "unknown";
    const userAgent = req.headers.get("User-Agent") || "unknown";
    return `auth:${ip}:${userAgent}`;
  },
});

/**
 * General API rate limiting
 */
export const apiRateLimitMiddleware = rateLimitMiddleware({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per 15 minutes
  message: "API rate limit exceeded, please try again later.",
});

/**
 * Lenient rate limiting for public endpoints
 */
export const publicRateLimitMiddleware = rateLimitMiddleware({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 1000, // 1000 requests per 15 minutes
  message: "Rate limit exceeded, please try again later.",
  skipSuccessfulRequests: true, // Don't count successful requests
});

/**
 * AI Summary rate limiting for quick summaries (5 per day)
 */
export const aiQuickSummaryRateLimitMiddleware = rateLimitMiddleware({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  maxRequests: 5, // 5 quick summaries per day
  message:
    "Daily limit for quick summaries exceeded (5/day). Please try again tomorrow.",
  keyGenerator: (req: BunRequest) => {
    // Use user ID from auth middleware for user-specific rate limiting
    const authReq = req as any;
    const userId = authReq.user?.id;
    if (!userId) {
      // Fallback to IP if no user (shouldn't happen for authenticated routes)
      return `ai-quick:${req.headers.get("X-Forwarded-For") || "unknown"}`;
    }
    return `ai-quick:${userId}`;
  },
});

/**
 * AI Summary rate limiting for detailed summaries (1 per day)
 */
export const aiDetailedSummaryRateLimitMiddleware = rateLimitMiddleware({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  maxRequests: 1, // 1 detailed summary per day
  message:
    "Daily limit for detailed summaries exceeded (1/day). Please try again tomorrow.",
  keyGenerator: (req: BunRequest) => {
    // Use user ID from auth middleware for user-specific rate limiting
    const authReq = req as any;
    const userId = authReq.user?.id;
    if (!userId) {
      // Fallback to IP if no user (shouldn't happen for authenticated routes)
      return `ai-detailed:${req.headers.get("X-Forwarded-For") || "unknown"}`;
    }
    return `ai-detailed:${userId}`;
  },
});
