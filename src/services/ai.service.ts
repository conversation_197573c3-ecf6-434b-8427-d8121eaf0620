import { GEMINI_API_KEY, GEMINI_API_URL } from "../config/env";
import { AppError, BadRequestError, ServiceUnavailableError } from "../utils/error";

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
    index: number;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

export interface SummaryOptions {
  type: "quick" | "detailed";
  url: string;
  title: string;
  content?: string;
}

export class AIService {
  private static readonly QUICK_SUMMARY_PROMPT = `
Please provide a concise summary of the content from this URL. Focus on the main points and key takeaways.

URL: {url}
Title: {title}

Keep the summary brief, informative, and under 200 words.
`;

  private static readonly DETAILED_SUMMARY_PROMPT = `
Please provide a comprehensive summary of the content from this URL. Include:
1. Main topic and purpose
2. Key points and arguments
3. Important details and examples
4. Conclusions or takeaways

URL: {url}
Title: {title}

Provide a thorough analysis while keeping it well-structured and readable.
`;

  private static validateApiKey(): void {
    if (!GEMINI_API_KEY) {
      throw new ServiceUnavailableError(
        "AI service is not configured. Please contact administrator."
      );
    }
  }

  private static getPrompt(options: SummaryOptions): string {
    const template = options.type === "quick" 
      ? this.QUICK_SUMMARY_PROMPT 
      : this.DETAILED_SUMMARY_PROMPT;
    
    return template
      .replace("{url}", options.url)
      .replace("{title}", options.title);
  }

  private static async makeGeminiRequest(prompt: string): Promise<GeminiResponse> {
    const model = "gemini-1.5-flash-8b";
    const url = `${GEMINI_API_URL}/${model}:generateContent?key=${GEMINI_API_KEY}`;

    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.3,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    };

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Gemini API error:", {
          status: response.status,
          statusText: response.statusText,
          error: errorData,
        });

        if (response.status === 429) {
          throw new ServiceUnavailableError(
            "AI service is temporarily unavailable due to rate limiting. Please try again later."
          );
        }

        if (response.status === 400) {
          throw new BadRequestError(
            "Invalid request to AI service. Please check your input."
          );
        }

        throw new ServiceUnavailableError(
          "AI service is temporarily unavailable. Please try again later."
        );
      }

      const data: GeminiResponse = await response.json();
      return data;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }

      console.error("Gemini API request failed:", error);
      throw new ServiceUnavailableError(
        "Failed to connect to AI service. Please try again later."
      );
    }
  }

  public static async generateSummary(options: SummaryOptions): Promise<{
    summary: string;
    tokensUsed: number;
    model: string;
  }> {
    this.validateApiKey();

    const prompt = this.getPrompt(options);
    const startTime = Date.now();

    try {
      const response = await this.makeGeminiRequest(prompt);

      if (!response.candidates || response.candidates.length === 0) {
        throw new ServiceUnavailableError(
          "AI service did not return a valid response. Please try again."
        );
      }

      const candidate = response.candidates[0];
      if (!candidate?.content?.parts || candidate.content.parts.length === 0) {
        throw new ServiceUnavailableError(
          "AI service returned an empty response. Please try again."
        );
      }

      const summary = candidate.content.parts[0]?.text;
      if (!summary || summary.trim().length === 0) {
        throw new ServiceUnavailableError(
          "AI service returned an empty summary. Please try again."
        );
      }

      const tokensUsed = response.usageMetadata?.totalTokenCount || 0;
      const processingTime = Date.now() - startTime;

      console.log(`AI Summary generated: ${processingTime}ms, ${tokensUsed} tokens`);

      return {
        summary: summary.trim(),
        tokensUsed,
        model: "gemini-1.5-flash-8b",
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`AI Summary failed after ${processingTime}ms:`, error);
      throw error;
    }
  }
}
