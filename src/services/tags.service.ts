import { Types } from "mongoose";
import { Tags } from "../models/tags.model";
import type { TagsTypes } from "../schema/tags.schema";
import { BadRequestError, ConflictError, NotFoundError } from "../utils/error";

/**
 * Create a new tag for a specific user
 */
export async function CreateTagService(tagData: TagsTypes, userId: string) {
  try {
    // Check if tag already exists for this user (case-insensitive)
    const existingTag = await Tags.findOne({
      title: tagData.title.toLowerCase().trim(),
      userId: new Types.ObjectId(userId),
    });

    if (existingTag) {
      throw new ConflictError(
        `Tag "${tagData.title}" already exists in your tags.`
      );
    }

    const newTag = new Tags({
      title: tagData.title.toLowerCase().trim(),
      userId: new Types.ObjectId(userId),
    });

    await newTag.save();
    return newTag.toObject();
  } catch (error) {
    if (error instanceof ConflictError) {
      throw error;
    }
    // Handle MongoDB duplicate key error
    if ((error as any).code === 11000) {
      throw new ConflictError(
        `Tag "${tagData.title}" already exists in your tags.`
      );
    }
    throw new BadRequestError("Failed to create tag.");
  }
}

/**
 * Get all tags for a specific user with pagination and search
 */
export async function GetAllTagsService(
  userId: string,
  page: number = 1,
  limit: number = 20,
  search?: string
) {
  try {
    const skip = (page - 1) * limit;

    // Build search query for user's tags only
    const query: any = { userId: new Types.ObjectId(userId) };
    if (search) {
      query.title = {
        $regex: search.toLowerCase().trim(),
        $options: "i",
      };
    }

    // Get tags with pagination
    const [tags, totalCount] = await Promise.all([
      Tags.find(query)
        .sort({ title: 1 }) // Sort alphabetically
        .skip(skip)
        .limit(limit)
        .lean(),
      Tags.countDocuments(query),
    ]);

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      tags,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit,
      },
    };
  } catch (error) {
    throw new BadRequestError("Failed to fetch tags.");
  }
}

/**
 * Get a single tag by ID for a specific user
 */
export async function GetTagByIdService(tagId: string, userId: string) {
  try {
    if (!Types.ObjectId.isValid(tagId)) {
      throw new BadRequestError("Invalid tag ID format.");
    }

    const tag = await Tags.findOne({
      _id: tagId,
      userId: new Types.ObjectId(userId),
    }).lean();

    if (!tag) {
      throw new NotFoundError(
        "Tag not found or you don't have permission to access it."
      );
    }

    return tag;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to fetch tag.");
  }
}

/**
 * Update a tag by ID for a specific user
 */
export async function UpdateTagService(
  tagId: string,
  userId: string,
  updateData: TagsTypes
) {
  try {
    if (!Types.ObjectId.isValid(tagId)) {
      throw new BadRequestError("Invalid tag ID format.");
    }

    // Check if tag exists and belongs to user
    const existingTag = await Tags.findOne({
      _id: tagId,
      userId: new Types.ObjectId(userId),
    });
    if (!existingTag) {
      throw new NotFoundError(
        "Tag not found or you don't have permission to modify it."
      );
    }

    // Check if new title conflicts with existing tags for this user (excluding current tag)
    const titleConflict = await Tags.findOne({
      title: updateData.title.toLowerCase().trim(),
      userId: new Types.ObjectId(userId),
      _id: { $ne: tagId },
    });

    if (titleConflict) {
      throw new ConflictError(
        `Tag "${updateData.title}" already exists in your tags.`
      );
    }

    const updatedTag = await Tags.findOneAndUpdate(
      { _id: tagId, userId: new Types.ObjectId(userId) },
      { title: updateData.title.toLowerCase().trim() },
      { new: true, runValidators: true }
    ).lean();

    return updatedTag;
  } catch (error) {
    if (
      error instanceof BadRequestError ||
      error instanceof NotFoundError ||
      error instanceof ConflictError
    ) {
      throw error;
    }
    // Handle MongoDB duplicate key error
    if ((error as any).code === 11000) {
      throw new ConflictError(
        `Tag "${updateData.title}" already exists in your tags.`
      );
    }
    throw new BadRequestError("Failed to update tag.");
  }
}

/**
 * Delete a tag by ID
 */
export async function DeleteTagService(userId: string, tagId: string) {
  try {
    if (!Types.ObjectId.isValid(tagId)) {
      throw new BadRequestError("Invalid tag ID format.");
    }

    const deletedTag = await Tags.findOneAndDelete({
      _id: new Types.ObjectId(tagId),
      userId: new Types.ObjectId(userId),
    }).lean();

    if (!deletedTag) {
      throw new NotFoundError(
        "Tag not found or you don't have permission to delete it."
      );
    }

    // Remove this tag from all content that references it
    const { Content } = await import("../models/content.model");
    const updateResult = await Content.updateMany(
      {
        tags: tagId,
        userId: new Types.ObjectId(userId),
      },
      {
        $pull: { tags: tagId },
      }
    );

    console.log(
      `Removed tag ${tagId} from ${updateResult.modifiedCount} content items`
    );

    return deletedTag;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to delete tag.");
  }
}

/**
 * Get tags by multiple IDs for a specific user (useful for content creation/update)
 */
export async function GetTagsByIdsService(tagIds: string[], userId: string) {
  try {
    // Validate all tag IDs
    const invalidIds = tagIds.filter((id) => !Types.ObjectId.isValid(id));
    if (invalidIds.length > 0) {
      throw new BadRequestError(
        `Invalid tag ID format: ${invalidIds.join(", ")}`
      );
    }

    const tags = await Tags.find({
      _id: { $in: tagIds.map((id) => new Types.ObjectId(id)) },
      userId: new Types.ObjectId(userId),
    }).lean();

    // Check if all requested tags were found and belong to the user
    if (tags.length !== tagIds.length) {
      const foundIds = tags.map((tag) => tag._id.toString());
      const missingIds = tagIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundError(
        `Tags not found or not accessible: ${missingIds.join(", ")}`
      );
    }

    return tags;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to fetch tags.");
  }
}

/**
 * Search tags by title for a specific user (for autocomplete/suggestions)
 */
export async function SearchTagsService(
  searchTerm: string,
  userId: string,
  limit: number = 10
) {
  try {
    if (!searchTerm || searchTerm.trim().length === 0) {
      return [];
    }

    const tags = await Tags.find({
      title: {
        $regex: searchTerm.toLowerCase().trim(),
        $options: "i",
      },
      userId: new Types.ObjectId(userId),
    })
      .sort({ title: 1 })
      .limit(limit)
      .lean();

    return tags;
  } catch (error) {
    throw new BadRequestError("Failed to search tags.");
  }
}
