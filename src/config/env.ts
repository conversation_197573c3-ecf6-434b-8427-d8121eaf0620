export const MONGO_URI =
  process.env.MONGO_URI || "mongodb://localhost:27017/second_brain";
export const PORT = parseInt(process.env.PORT || "3000", 10); // Fix: parseInt, not parseint
export const APP_BASE_URL =
  process.env.APP_BASE_URL || `http://localhost:${PORT}`;
export const JWT_ACCESS_SECRET: string =
  process.env.JWT_ACCESS_SECRET || "default_access_secret";
export const JWT_REFRESH_SECRET: string =
  process.env.JWT_REFRESH_SECRET || "default_refresh_secret"; // New
export const JWT_ACCESS_EXPIRES_IN: string =
  process.env.JWT_ACCESS_EXPIRES_IN || "15m"; // New
export const JWT_REFRESH_EXPIRES_IN: string =
  process.env.JWT_REFRESH_EXPIRES_IN || "7d"; // New

// basic check
if (!process.env.MONGO_URI) {
  console.warn("MONGO_URI is not set in .env. Using default."); // FIX THIS LINE
}
// Add checks for new env vars
if (!process.env.JWT_ACCESS_SECRET) {
  console.warn("JWT_ACCESS_SECRET is not set in .env. Using default.");
}
if (!process.env.JWT_REFRESH_SECRET) {
  console.warn("JWT_REFRESH_SECRET is not set in .env. Using default.");
}
if (!process.env.JWT_ACCESS_EXPIRES_IN) {
  console.warn("JWT_ACCESS_EXPIRES_IN is not set in .env. Using default.");
}
if (!process.env.JWT_REFRESHE_EXPIRES_IN) {
  // Check for typo here as well
  console.warn("JWT_REFRESH_EXPIRES_IN is not set in .env. Using default.");
}
