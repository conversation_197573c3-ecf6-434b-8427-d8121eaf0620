import { model, Schema, Types } from "mongoose";

const SUMMARY_TYPE = ["quick", "detailed"];

const SummarySchema = new Schema(
  {
    contentId: {
      type: Types.ObjectId,
      ref: "Content",
      required: true,
      index: true,
    },
    userId: {
      type: Types.ObjectId,
      ref: "User",
      required: true,
      index: true,
    },
    type: {
      type: String,
      enum: SUMMARY_TYPE,
      required: true,
      lowercase: true,
      trim: true,
    },
    summary: {
      type: String,
      required: true,
      trim: true,
      minlength: [10, "Summary must be at least 10 characters long."],
      maxlength: [5000, "Summary cannot exceed 5000 characters."],
    },
    originalUrl: {
      type: String,
      required: true,
      trim: true,
    },
    contentTitle: {
      type: String,
      required: true,
      trim: true,
    },
    tokensUsed: {
      type: Number,
      default: 0,
      min: [0, "Tokens used cannot be negative."],
    },
    processingTime: {
      type: Number, // in milliseconds
      default: 0,
      min: [0, "Processing time cannot be negative."],
    },
    model: {
      type: String,
      default: "gemini-1.5-flash-8b",
      trim: true,
    },
    status: {
      type: String,
      enum: ["pending", "completed", "failed"],
      default: "pending",
      lowercase: true,
    },
    errorMessage: {
      type: String,
      trim: true,
    },
  },
  { 
    timestamps: true,
    // Create compound index for efficient queries
    indexes: [
      { contentId: 1, userId: 1, type: 1 },
      { userId: 1, createdAt: -1 },
      { status: 1, createdAt: -1 }
    ]
  }
);

// Prevent duplicate summaries for same content, user, and type
SummarySchema.index(
  { contentId: 1, userId: 1, type: 1 },
  { unique: true }
);

export const Summary = model("Summary", SummarySchema);
export { SUMMARY_TYPE };
