import { model, Schema, Types } from "mongoose";

const CONTENT_TYPE = ["image", "video", "artical", "audio"];

const ContentSchema = new Schema(
  {
    link: {
      type: String,
      required: [true, "Link is empty!"],
      trim: true,
    },
    type: {
      type: String,
      enum: CONTENT_TYPE,
      required: [true, "Content type is empty!"],
      lowercase: true,
      trim: true,
    },
    title: {
      type: String,
      required: [true, "Title is empty!"],
      trim: true,
      minlength: [3, "Title must be atleast 3 characters long."],
      maxlength: [150, "Title cannot exceed 150 characters."],
    },
    tags: [{ type: Types.ObjectId, ref: "Tag" }],
    userId: { type: Types.ObjectId, ref: "User", required: true, index: true },
  },
  { timestamps: true }
);

export const Content = model("Content", ContentSchema);
