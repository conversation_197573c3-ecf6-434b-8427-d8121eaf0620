import { Document, model, Schema, Types } from "mongoose";

export interface ITags extends Document {
  title: string;
  userId: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const TagsSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, "Please provide a tag for this content"],
      lowercase: true,
      trim: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: [true, "User ID is required"],
    },
  },
  { timestamps: true }
);

// Create compound unique index to ensure tag titles are unique per user
TagsSchema.index({ title: 1, userId: 1 }, { unique: true });

export const Tags = model<ITags>("Tag", TagsSchema);
