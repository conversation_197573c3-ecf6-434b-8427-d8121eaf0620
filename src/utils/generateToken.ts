import jwt, { type SignOptions, type JwtPayload } from "jsonwebtoken";
import {
  JWT_ACCESS_SECRET,
  JWT_REFRESH_SECRET,
  JWT_ACCESS_EXPIRES_IN,
  JWT_REFRESH_EXPIRES_IN,
} from "../config/env";
import bcrypt from "bcryptjs";

export const generateAccessToken = (token: string): string => {
  const decoded = jwt.verify(token, JWT_REFRESH_SECRET);

  // Handle the case where jwt.verify returns a string or JwtPayload
  if (typeof decoded === "string") {
    throw new Error("Invalid token format");
  }

  const payload = decoded as JwtPayload & { id: string };

  if (!payload.id) {
    throw new Error("Token does not contain user ID");
  }

  return jwt.sign({ id: payload.id }, JWT_ACCESS_SECRET, {
    expiresIn: JWT_ACCESS_EXPIRES_IN,
  } as SignOptions);
};

export const generateRefreshToken = (id: string): string => {
  return jwt.sign({ id }, JWT_REFRESH_SECRET, {
    expiresIn: JWT_REFRESH_EXPIRES_IN,
  } as SignOptions);
};

export const hashToken = async (token: string): Promise<string> => {
  return await bcrypt.hash(token, 10);
};
