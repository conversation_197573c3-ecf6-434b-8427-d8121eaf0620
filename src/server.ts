// Core dependencies
import { serve } from "bun";
import { ZodError } from "zod/v4";

// Configuration
import { connectDB } from "./config/db";
import { PORT } from "./config/env";

// Controllers - Authentication
import {
  User<PERSON><PERSON><PERSON><PERSON><PERSON>roller,
  UserLogoutController,
  UserRefreshTokenController,
  UserSignupController,
} from "./controllers/user.controller";

// Controllers - Content
import {
  BulkDeleteContentController,
  ContentCreateController,
  DeleteContentController,
  GetContentByIdController,
  GetContentStatsController,
  GetUserContentController,
  UpdateUserContentController,
} from "./controllers/content.controller";

// Controllers - Tags
import {
  CreateTagController,
  DeleteTagController,
  GetAllTagsController,
  GetTagByIdController,
  SearchTagsController,
  UpdateTagController,
} from "./controllers/tags.controller";

// Controllers - Shareable Links
import {
  AccessSharedContentController,
  CreateShareableLinkController,
  DeleteShareableLinkController,
} from "./controllers/link.controller";

// Controllers - AI Summaries
import {
  GenerateSummaryController,
  GetSummaryByIdController,
  GetUserSummariesController,
  DeleteSummaryController,
  GenerateQuickSummaryController,
  GenerateDetailedSummaryController,
} from "./controllers/summary.controller";

// Middleware
import {
  aiDetailedSummaryRateLimitMiddleware,
  aiQuickSummaryRateLimitMiddleware,
  apiRateLimitMiddleware,
  authMiddleware,
  authRateLimitMiddleware,
  corsMiddleware,
  detailedLoggingMiddleware,
  errorBoundaryMiddleware,
  publicRateLimitMiddleware,
  requestValidationMiddleware,
  securityHeadersMiddleware,
  simpleLoggingMiddleware,
  withMiddleware,
} from "./middleware/index.middleware";

// Utils
import { AppError } from "./utils/error";

connectDB();

// Configure CORS based on environment
const corsConfig = corsMiddleware({
  origin:
    process.env.NODE_ENV === "production"
      ? ["https://yourdomain.com", "https://www.yourdomain.com"]
      : true, // Allow all origins in development
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
});

const loggingConfig =
  process.env.NODE_ENV === "development"
    ? detailedLoggingMiddleware
    : simpleLoggingMiddleware;

// Shared middleware configurations
const baseMiddleware = [
  loggingConfig,
  errorBoundaryMiddleware,
  corsConfig,
  securityHeadersMiddleware,
];

const publicMiddleware = [...baseMiddleware, publicRateLimitMiddleware];

const authMiddlewareStack = [
  ...baseMiddleware,
  requestValidationMiddleware,
  authRateLimitMiddleware,
];

const apiMiddlewareStack = [
  ...baseMiddleware,
  requestValidationMiddleware,
  apiRateLimitMiddleware,
  authMiddleware,
];

const apiMiddlewareStackNoValidation = [
  ...baseMiddleware,
  apiRateLimitMiddleware,
  authMiddleware,
];

const server = serve({
  port: PORT,
  // `routes` requires Bun v1.2.3+
  routes: {
    // Root and status routes
    "/": withMiddleware(
      async () =>
        Response.json(
          {
            message:
              "Server is running fine, but this not the correct route to hit.",
          },
          { status: 200 }
        ),
      ...publicMiddleware
    ),
    "/api/status": new Response("OK"),

    // Authentication routes
    "/api/v1/login": {
      POST: withMiddleware(UserLoginController, ...authMiddlewareStack),
    },
    "/api/v1/signup": {
      POST: withMiddleware(UserSignupController, ...authMiddlewareStack),
    },
    "/api/v1/logout": {
      POST: withMiddleware(UserLogoutController, ...authMiddlewareStack),
    },
    "/api/v1/refresh": {
      POST: withMiddleware(UserRefreshTokenController, ...authMiddlewareStack),
    },
    // Content routes
    "/api/v1/user/content": {
      POST: withMiddleware(ContentCreateController, ...apiMiddlewareStack),
      GET: withMiddleware(GetUserContentController, ...apiMiddlewareStack),
    },
    "/api/v1/user/content/stats": {
      GET: withMiddleware(
        GetContentStatsController,
        ...apiMiddlewareStackNoValidation
      ),
    },
    "/api/v1/user/content/bulk": {
      DELETE: withMiddleware(
        BulkDeleteContentController,
        ...apiMiddlewareStack
      ),
    },

    // Tags routes
    "/api/v1/tags": {
      GET: withMiddleware(
        GetAllTagsController,
        ...apiMiddlewareStackNoValidation
      ),
      POST: withMiddleware(CreateTagController, ...apiMiddlewareStack),
    },
    "/api/v1/tags/search": {
      GET: withMiddleware(
        SearchTagsController,
        ...apiMiddlewareStackNoValidation
      ),
    },

    // Shareable Links routes
    "/api/v1/links": {
      POST: withMiddleware(
        CreateShareableLinkController,
        ...apiMiddlewareStack
      ),
    },

    // Public shareable link access (no auth required)
    "/share/*": {
      GET: withMiddleware(AccessSharedContentController, ...publicMiddleware),
    },

    // AI Summary routes
    "/api/v1/summaries": {
      GET: withMiddleware(
        GetUserSummariesController,
        ...apiMiddlewareStackNoValidation
      ),
      POST: withMiddleware(GenerateSummaryController, ...apiMiddlewareStack),
    },

    "/api/v1/summaries/quick": {
      POST: withMiddleware(
        GenerateQuickSummaryController,
        ...baseMiddleware,
        authMiddleware,
        aiQuickSummaryRateLimitMiddleware,
        requestValidationMiddleware
      ),
    },

    "/api/v1/summaries/detailed": {
      POST: withMiddleware(
        GenerateDetailedSummaryController,
        ...baseMiddleware,
        authMiddleware,
        aiDetailedSummaryRateLimitMiddleware,
        requestValidationMiddleware
      ),
    },

    // Dynamic Tag Routes - Handle individual tag operations
    "/api/v1/tags/*": async (req: Request) => {
      const url = new URL(req.url);
      const path = url.pathname;
      const method = req.method;

      // Handle /api/v1/tags/:id
      const tagIdMatch = path.match(/^\/api\/v1\/tags\/([a-fA-F0-9]{24})$/);
      if (tagIdMatch) {
        const controller =
          method === "GET"
            ? GetTagByIdController
            : method === "PUT"
            ? UpdateTagController
            : method === "DELETE"
            ? DeleteTagController
            : async () =>
                Response.json(
                  { success: false, message: "Method not allowed" },
                  { status: 405 }
                );

        const middlewareStack =
          method === "GET"
            ? apiMiddlewareStackNoValidation
            : apiMiddlewareStack;

        const middleware = withMiddleware(controller, ...middlewareStack);
        return await middleware(req as any);
      }

      return Response.json(
        { success: false, message: "Tag endpoint not found" },
        { status: 404 }
      );
    },

    // Dynamic Content Routes - Handle individual content operations
    "/api/v1/user/content/*": async (req: Request) => {
      const url = new URL(req.url);
      const path = url.pathname;
      const method = req.method;

      // Handle /api/v1/user/content/:id
      const contentIdMatch = path.match(
        /^\/api\/v1\/user\/content\/([a-fA-F0-9]{24})$/
      );
      if (contentIdMatch) {
        const controller =
          method === "GET"
            ? GetContentByIdController
            : method === "PUT"
            ? UpdateUserContentController
            : method === "DELETE"
            ? DeleteContentController
            : async () =>
                Response.json(
                  { success: false, message: "Method not allowed" },
                  { status: 405 }
                );

        const middlewareStack =
          method === "GET"
            ? apiMiddlewareStackNoValidation
            : apiMiddlewareStack;

        const middleware = withMiddleware(controller, ...middlewareStack);
        return await middleware(req as any);
      }

      return Response.json(
        { success: false, message: "Content endpoint not found" },
        { status: 404 }
      );
    },

    // Dynamic Link Routes - Handle individual link operations
    "/api/v1/links/*": async (req: Request) => {
      const url = new URL(req.url);
      const path = url.pathname;
      const method = req.method;

      // Handle /api/v1/links/:id (only DELETE allowed)
      const linkIdMatch = path.match(/^\/api\/v1\/links\/([a-fA-F0-9]{24})$/);
      if (linkIdMatch) {
        const controller =
          method === "DELETE"
            ? DeleteShareableLinkController
            : async () =>
                Response.json(
                  { success: false, message: "Method not allowed" },
                  { status: 405 }
                );

        const middleware = withMiddleware(controller, ...apiMiddlewareStack);
        return await middleware(req as any);
      }

      return Response.json(
        { success: false, message: "Link endpoint not found" },
        { status: 404 }
      );
    },

    // Dynamic Summary Routes - Handle individual summary operations
    "/api/v1/summaries/*": async (req: Request) => {
      const url = new URL(req.url);
      const path = url.pathname;
      const method = req.method;

      // Handle /api/v1/summaries/:id
      const summaryIdMatch = path.match(
        /^\/api\/v1\/summaries\/([a-fA-F0-9]{24})$/
      );
      if (summaryIdMatch) {
        const controller =
          method === "GET"
            ? GetSummaryByIdController
            : method === "DELETE"
            ? DeleteSummaryController
            : async () =>
                Response.json(
                  { success: false, message: "Method not allowed" },
                  { status: 405 }
                );

        const middlewareStack =
          method === "GET"
            ? apiMiddlewareStackNoValidation
            : apiMiddlewareStack;

        const middleware = withMiddleware(controller, ...middlewareStack);
        return await middleware(req as any);
      }

      return Response.json(
        { success: false, message: "Summary endpoint not found" },
        { status: 404 }
      );
    },

    // Catch-all for unmatched API routes
    "/api/*": withMiddleware(async (req: Request) => {
      const url = new URL(req.url);
      const method = req.method;

      return Response.json(
        {
          success: false,
          message: "API endpoint not found",
          path: url.pathname,
          method: method,
        },
        { status: 404 }
      );
    }, ...publicMiddleware),
  },

  // Fallback for unmatched routes
  async fetch(_req: Request) {
    return new Response("Not Found", { status: 404 });
  },

  error(err: Error): Response {
    console.error("Caught by Bun.server global error handler:", err);

    let statusCode = 500;
    let message = "Internal Server Error";
    let errors: any[] | undefined = undefined;

    if (err instanceof AppError) {
      statusCode = err.statusCode;
      message = err.message;

      if (!err.isOperational) {
        console.error("Non-operational error (likely a bug):", err.stack);
      }
    } else if ((err as any).code === 11000) {
      statusCode = 409;
      message = "User already exists.";
      if ((err as any).keyValue) {
        message += `(Key: ${Object.keys((err as any).keyValue).join(",")})`;
      }
    } else if (err instanceof ZodError) {
      statusCode = 400;
      message = "Validation failed";
      errors = err.issues.map((e) => ({
        path: e.path.join("."),
        message: e.message,
      }));
    } else {
      if (process.env.NODE_ENV === "production") {
        message = "An unexpected server error occured.";
      } else {
        message = `An unexpected server error occured: ${err.message}`;
      }
    }

    return Response.json(
      {
        success: false,
        message: message,
        errors: errors,
      },
      {
        status: statusCode,
        headers: { "Content-Type": "application/json" },
      }
    );
  },
});

console.log(`🚀 Server running at ${server.url}`);
