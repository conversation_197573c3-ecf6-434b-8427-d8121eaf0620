# 🤖 AI Summary API Gateway

An intelligent content summarization system powered by Google Gemini 1.5 Flash for your Second Brain application.

## ✨ Features

- **Quick Summaries**: Concise 2-3 sentence summaries (5 per day limit)
- **Detailed Summaries**: Comprehensive analysis with key points (1 per day limit)
- **Smart Caching**: Avoid regenerating summaries for the same content
- **Rate Limiting**: User-specific daily limits to manage API costs
- **Error Handling**: Robust error handling with meaningful messages
- **Authentication**: Secure, user-scoped summaries

## 🚀 API Endpoints

### Generate Quick Summary
```http
POST /api/v1/summaries/quick
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "contentId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "forceRegenerate": false
}
```

### Generate Detailed Summary
```http
POST /api/v1/summaries/detailed
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "contentId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "forceRegenerate": false
}
```

### Get All User Summaries
```http
GET /api/v1/summaries?page=1&limit=10
Authorization: Bearer <access_token>
```

### Get Summary by ID
```http
GET /api/v1/summaries/{summaryId}
Authorization: Bearer <access_token>
```

### Delete Summary
```http
DELETE /api/v1/summaries/{summaryId}
Authorization: Bearer <access_token>
```

## 📊 Rate Limits

- **Quick Summaries**: 5 per day per user
- **Detailed Summaries**: 1 per day per user
- Rate limits reset at midnight UTC
- Rate limit headers included in responses

## 🔧 Setup Instructions

### 1. Get Google Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key for your environment variables

### 2. Environment Configuration
Copy `.env.example` to `.env` and update:

```bash
# Required for AI features
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models
```

### 3. Database Schema
The system automatically creates the following collections:
- `summaries` - Stores generated summaries with metadata

### 4. Start the Server
```bash
bun run dev
```

## 🧪 Testing

Run the test script to verify everything works:

```bash
bun run test-ai-summary.js
```

This will:
1. Create a test user
2. Create test content
3. Generate quick and detailed summaries
4. Test rate limiting
5. Verify all endpoints work correctly

## 📝 Response Format

### Success Response
```json
{
  "success": true,
  "message": "Summary generated successfully.",
  "data": {
    "id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "contentId": "64f8a1b2c3d4e5f6a7b8c9d0",
    "userId": "64f8a1b2c3d4e5f6a7b8c9d0",
    "type": "quick",
    "summary": "This article discusses...",
    "originalUrl": "https://example.com/article",
    "contentTitle": "Example Article",
    "tokensUsed": 150,
    "processingTime": 1250,
    "model": "gemini-1.5-flash-8b",
    "status": "completed",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### Rate Limit Response
```json
{
  "success": false,
  "message": "Daily limit for quick summaries exceeded (5/day). Please try again tomorrow.",
  "retryAfter": 3600
}
```

## 🛡️ Security Features

- **User Authentication**: All endpoints require valid JWT tokens
- **User Isolation**: Users can only access their own summaries
- **Rate Limiting**: Prevents API abuse and controls costs
- **Input Validation**: Comprehensive validation using Zod schemas
- **Error Sanitization**: Safe error messages without sensitive data

## 💰 Cost Management

- **Efficient Prompts**: Optimized prompts for minimal token usage
- **Smart Caching**: Reuse existing summaries when possible
- **Rate Limiting**: Daily limits prevent unexpected costs
- **Token Tracking**: Monitor usage with detailed metrics

## 🔄 Frontend Integration

### Quick Summary Button
```javascript
async function generateQuickSummary(contentId) {
  try {
    const response = await fetch('/api/v1/summaries/quick', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({ contentId })
    });
    
    const data = await response.json();
    
    if (data.success) {
      displaySummary(data.data.summary);
    } else {
      showError(data.message);
    }
  } catch (error) {
    showError('Failed to generate summary');
  }
}
```

### Detailed Summary Button
```javascript
async function generateDetailedSummary(contentId) {
  try {
    const response = await fetch('/api/v1/summaries/detailed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({ contentId })
    });
    
    const data = await response.json();
    
    if (data.success) {
      displayDetailedSummary(data.data.summary);
    } else {
      showError(data.message);
    }
  } catch (error) {
    showError('Failed to generate detailed summary');
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **"AI service is not configured"**
   - Check that `GEMINI_API_KEY` is set in your `.env` file
   - Verify the API key is valid

2. **"Rate limit exceeded"**
   - User has reached daily limit
   - Wait until next day or implement premium features

3. **"Content not found"**
   - Ensure the content exists and belongs to the authenticated user
   - Check that the contentId is a valid MongoDB ObjectId

4. **"Failed to connect to AI service"**
   - Check internet connection
   - Verify Gemini API is accessible
   - Check API key permissions

## 📈 Monitoring

The system logs important metrics:
- Summary generation time
- Token usage
- Error rates
- Rate limit hits

Monitor these logs to optimize performance and costs.

## 🔮 Future Enhancements

- [ ] Support for different AI models
- [ ] Batch summary generation
- [ ] Summary quality ratings
- [ ] Custom prompt templates
- [ ] Summary sharing features
- [ ] Analytics dashboard
