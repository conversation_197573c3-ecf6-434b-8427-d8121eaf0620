// Simple test script to verify AI summary functionality
// Run with: bun run test-ai-summary.js

const BASE_URL = "http://localhost:3000";

// Test data
const testUser = {
  username: "testuser",
  fullName: "Test User",
  password: "testpassword123"
};

const testContent = {
  link: "https://example.com/article",
  type: "artical",
  title: "Test Article for Summary"
};

async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
      ...options.headers
    },
    ...options
  });
  
  const data = await response.json();
  return { response, data };
}

async function testAISummary() {
  console.log("🧪 Testing AI Summary API Gateway...\n");

  try {
    // Step 1: Check server status
    console.log("1. Checking server status...");
    const { response: statusResponse } = await makeRequest("/api/status");
    if (statusResponse.ok) {
      console.log("✅ Server is running\n");
    } else {
      throw new Error("Server is not responding");
    }

    // Step 2: Sign up a test user
    console.log("2. Creating test user...");
    const { data: signupData } = await makeRequest("/api/v1/signup", {
      method: "POST",
      body: JSON.stringify(testUser)
    });
    
    if (!signupData.success) {
      console.log("⚠️  User might already exist, trying to login...");
      
      // Try to login instead
      const { data: loginData } = await makeRequest("/api/v1/login", {
        method: "POST",
        body: JSON.stringify({
          username: testUser.username,
          password: testUser.password
        })
      });
      
      if (!loginData.success) {
        throw new Error("Failed to login: " + loginData.message);
      }
      
      var accessToken = loginData.accessToken;
      console.log("✅ Logged in successfully\n");
    } else {
      var accessToken = signupData.accessToken;
      console.log("✅ User created successfully\n");
    }

    // Step 3: Create test content
    console.log("3. Creating test content...");
    const { data: contentData } = await makeRequest("/api/v1/user/content", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`
      },
      body: JSON.stringify(testContent)
    });

    if (!contentData.success) {
      throw new Error("Failed to create content: " + contentData.message);
    }

    const contentId = contentData.contentData._id;
    console.log("✅ Content created with ID:", contentId, "\n");

    // Step 4: Test quick summary generation
    console.log("4. Testing quick summary generation...");
    const { response: quickSummaryResponse, data: quickSummaryData } = await makeRequest("/api/v1/summaries/quick", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        contentId: contentId
      })
    });

    console.log("Quick summary response status:", quickSummaryResponse.status);
    console.log("Quick summary data:", JSON.stringify(quickSummaryData, null, 2));

    if (quickSummaryData.success) {
      console.log("✅ Quick summary generated successfully\n");
    } else {
      console.log("❌ Quick summary failed:", quickSummaryData.message, "\n");
    }

    // Step 5: Test detailed summary generation
    console.log("5. Testing detailed summary generation...");
    const { response: detailedSummaryResponse, data: detailedSummaryData } = await makeRequest("/api/v1/summaries/detailed", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        contentId: contentId
      })
    });

    console.log("Detailed summary response status:", detailedSummaryResponse.status);
    console.log("Detailed summary data:", JSON.stringify(detailedSummaryData, null, 2));

    if (detailedSummaryData.success) {
      console.log("✅ Detailed summary generated successfully\n");
    } else {
      console.log("❌ Detailed summary failed:", detailedSummaryData.message, "\n");
    }

    // Step 6: Test getting user summaries
    console.log("6. Testing get user summaries...");
    const { data: summariesData } = await makeRequest("/api/v1/summaries", {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`
      }
    });

    console.log("User summaries:", JSON.stringify(summariesData, null, 2));

    if (summariesData.success) {
      console.log("✅ Retrieved user summaries successfully\n");
    } else {
      console.log("❌ Failed to retrieve summaries:", summariesData.message, "\n");
    }

    // Step 7: Test rate limiting
    console.log("7. Testing rate limiting...");
    console.log("Attempting to generate multiple quick summaries...");
    
    for (let i = 0; i < 3; i++) {
      const { response: rateLimitResponse, data: rateLimitData } = await makeRequest("/api/v1/summaries/quick", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${accessToken}`
        },
        body: JSON.stringify({
          contentId: contentId,
          forceRegenerate: true
        })
      });

      console.log(`Attempt ${i + 1}: Status ${rateLimitResponse.status}`);
      if (rateLimitResponse.status === 429) {
        console.log("✅ Rate limiting is working correctly");
        break;
      }
    }

    console.log("\n🎉 AI Summary API Gateway test completed!");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error("Stack trace:", error.stack);
  }
}

// Run the test
testAISummary();
