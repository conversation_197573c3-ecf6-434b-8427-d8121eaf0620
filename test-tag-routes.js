#!/usr/bin/env node

/**
 * Specific test for tag routes to verify all endpoints are working
 */

const BASE_URL = 'http://localhost:3000';

// Test data
const testUser = {
  fullName: 'Tag Test User',
  username: 'tagtest123',
  password: 'testpass123'
};

let accessToken = '';
let createdTagId = '';

// Helper function to make HTTP requests
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(accessToken && { 'Authorization': `Bearer ${accessToken}` })
    }
  };

  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  };

  try {
    console.log(`\n🔄 ${finalOptions.method || 'GET'} ${endpoint}`);
    const response = await fetch(url, finalOptions);
    const data = await response.json();
    
    console.log(`📊 Status: ${response.status}`);
    console.log(`📝 Response:`, JSON.stringify(data, null, 2));
    
    return { response, data };
  } catch (error) {
    console.error(`❌ Error making request to ${endpoint}:`, error.message);
    return { error };
  }
}

async function testLogin() {
  console.log('\n🔐 Testing Login...');
  const { response, data } = await makeRequest('/api/v1/login', {
    method: 'POST',
    body: JSON.stringify({
      username: testUser.username,
      password: testUser.password
    })
  });

  if (response?.status === 200 && data.accessToken) {
    accessToken = data.accessToken;
    console.log('✅ Login successful, access token obtained');
    return true;
  } else {
    console.log('❌ Login failed - trying signup first');
    return false;
  }
}

async function testSignup() {
  console.log('\n👤 Testing Signup...');
  const { response, data } = await makeRequest('/api/v1/signup', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });

  if (response?.status === 201 && data.accessToken) {
    accessToken = data.accessToken;
    console.log('✅ Signup successful, access token obtained');
    return true;
  } else {
    console.log('⚠️ Signup failed');
    return false;
  }
}

async function testCreateTag() {
  console.log('\n🏷️ Testing Create Tag (POST /api/v1/tags)...');
  const { response, data } = await makeRequest('/api/v1/tags', {
    method: 'POST',
    body: JSON.stringify({ title: 'test-tag-route' })
  });

  if (response?.status === 201 && data.data?._id) {
    createdTagId = data.data._id;
    console.log('✅ Tag created successfully');
    return true;
  } else {
    console.log('❌ Tag creation failed');
    return false;
  }
}

async function testGetAllTags() {
  console.log('\n📋 Testing Get All Tags (GET /api/v1/tags)...');
  const { response, data } = await makeRequest('/api/v1/tags');
  
  if (response?.status === 200) {
    console.log('✅ Get all tags successful');
    return true;
  } else {
    console.log('❌ Get all tags failed');
    return false;
  }
}

async function testGetTagById() {
  if (!createdTagId) {
    console.log('\n⚠️ Skipping Get Tag By ID - no tag created');
    return false;
  }
  
  console.log(`\n🔍 Testing Get Tag By ID (GET /api/v1/tags/${createdTagId})...`);
  const { response, data } = await makeRequest(`/api/v1/tags/${createdTagId}`);
  
  if (response?.status === 200) {
    console.log('✅ Get tag by ID successful');
    return true;
  } else {
    console.log('❌ Get tag by ID failed');
    return false;
  }
}

async function testUpdateTag() {
  if (!createdTagId) {
    console.log('\n⚠️ Skipping Update Tag - no tag created');
    return false;
  }
  
  console.log(`\n✏️ Testing Update Tag (PUT /api/v1/tags/${createdTagId})...`);
  const { response, data } = await makeRequest(`/api/v1/tags/${createdTagId}`, {
    method: 'PUT',
    body: JSON.stringify({ title: 'updated-test-tag-route' })
  });
  
  if (response?.status === 200) {
    console.log('✅ Update tag successful');
    return true;
  } else {
    console.log('❌ Update tag failed');
    return false;
  }
}

async function testSearchTags() {
  console.log('\n🔎 Testing Search Tags (GET /api/v1/tags/search)...');
  const { response, data } = await makeRequest('/api/v1/tags/search?q=test');
  
  if (response?.status === 200) {
    console.log('✅ Search tags successful');
    return true;
  } else {
    console.log('❌ Search tags failed');
    return false;
  }
}

async function testDeleteTag() {
  if (!createdTagId) {
    console.log('\n⚠️ Skipping Delete Tag - no tag created');
    return false;
  }
  
  console.log(`\n🗑️ Testing Delete Tag (DELETE /api/v1/tags/${createdTagId})...`);
  const { response, data } = await makeRequest(`/api/v1/tags/${createdTagId}`, {
    method: 'DELETE'
  });
  
  if (response?.status === 200) {
    console.log('✅ Delete tag successful');
    return true;
  } else {
    console.log('❌ Delete tag failed');
    return false;
  }
}

async function testInvalidTagRoute() {
  console.log('\n❌ Testing Invalid Tag Route (GET /api/v1/tags/invalid)...');
  const { response, data } = await makeRequest('/api/v1/tags/invalid');
  
  if (response?.status === 400 || response?.status === 404) {
    console.log('✅ Invalid route properly handled');
    return true;
  } else {
    console.log('❌ Invalid route not properly handled');
    return false;
  }
}

// Main test runner
async function runTagRouteTests() {
  console.log('🚀 Starting Tag Routes Test...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  
  const results = {
    passed: 0,
    failed: 0,
    total: 0
  };

  const tests = [
    { name: 'Login/Signup', fn: async () => (await testLogin()) || (await testSignup()) },
    { name: 'Create Tag', fn: testCreateTag },
    { name: 'Get All Tags', fn: testGetAllTags },
    { name: 'Get Tag By ID', fn: testGetTagById },
    { name: 'Update Tag', fn: testUpdateTag },
    { name: 'Search Tags', fn: testSearchTags },
    { name: 'Delete Tag', fn: testDeleteTag },
    { name: 'Invalid Route', fn: testInvalidTagRoute },
  ];

  for (const test of tests) {
    results.total++;
    try {
      const success = await test.fn();
      if (success) {
        results.passed++;
      } else {
        results.failed++;
      }
    } catch (error) {
      console.error(`💥 Test "${test.name}" threw an error:`, error.message);
      results.failed++;
    }
  }

  console.log('\n🎉 Tag Routes Test Completed!');
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${results.passed}/${results.total}`);
  console.log(`❌ Failed: ${results.failed}/${results.total}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);

  if (results.failed === 0) {
    console.log('\n🎊 All tag routes are working perfectly!');
  } else {
    console.log('\n⚠️ Some tag routes need attention.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTagRouteTests();
}

module.exports = { runTagRouteTests };
